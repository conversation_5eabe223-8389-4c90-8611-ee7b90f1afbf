import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/bloc/home_bloc.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/widgets/certificate_widget.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HomeCertificatesSection extends StatelessWidget {
  final HomeState state;
  final VoidCallback onShowAllPressed;

  const HomeCertificatesSection({
    super.key,
    required this.state,
    required this.onShowAllPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              Text(
                context.translate(LocalizationKeys.certificates),
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  fontSize: 16,
                  color: AppColors.colorSecondary,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: onShowAllPressed,
                child: Container(
                  width: 100,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color: AppColors.colorshowAll.withValues(alpha: 0.2),
                  ),
                  child: Center(
                    child: Text(
                      context.translate(LocalizationKeys.showAll),
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        color: AppColors.colorSecondary,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 10.h),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: _buildCertificatesList(context),
        ),
      ],
    );
  }

  Widget _buildCertificatesList(BuildContext context) {
    if (state is HomeLoadedState) {
      final loadedState = state as HomeLoadedState;

      if (loadedState.certificates.isEmpty) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Text(
            context.translate(LocalizationKeys.noCertificatesFound),
            style: TextStyle(fontSize: 14, color: AppColors.colorshowAll),
            textAlign: TextAlign.center,
          ),
        );
      }

      return Column(
        children:
            loadedState.certificates.map((certificate) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 20),
                child: CertificateWidget(certificate: certificate),
              );
            }).toList(),
      );
    }

    return Column(
      children: List.generate(
        4,
        (index) => Padding(
          padding: const EdgeInsets.only(bottom: 20),
          child: const CertificateWidget(),
        ),
      ),
    );
  }
}
